#!/bin/bash

# Set variables
NAMESPACE="dev"
EXTERNAL_SECRET_NAME="blacking-admin-dev-env-external"
SECRET_NAME="blacking-admin-dev-env-sync"

echo "Force Reload Environment Variables from S3"
echo "----------------------------------------"
echo "Executing all steps to force reload..."

# Step 1: Annotate ExternalSecret to force refresh
echo "1. Annotating ExternalSecret to force refresh..."
kubectl annotate externalsecret $EXTERNAL_SECRET_NAME -n $NAMESPACE "force-sync=true" --overwrite
echo "✓ Step 1 completed"
sleep 2

# Step 2: Delete Secret to trigger recreation
echo "2. Deleting Secret to trigger recreation..."
kubectl delete secret $SECRET_NAME -n $NAMESPACE
echo "✓ Step 2 completed"
sleep 2

# Step 3: Restart External Secrets controller
echo "3. Restarting External Secrets controller..."
kubectl rollout restart deployment -n external-secrets external-secrets
echo "✓ Step 3 completed"
sleep 2

# Step 4: Update ExternalSecret with timestamp annotation
echo "4. Updating ExternalSecret with timestamp annotation..."
timestamp=$(date +%s)
kubectl patch externalsecret $EXTERNAL_SECRET_NAME -n $NAMESPACE \
  --type=merge -p "{\"metadata\":{\"annotations\":{\"reloader.stakater.com/reload\":\"$timestamp\"}}}"
echo "✓ Step 4 completed"
sleep 2

# Step 5: Check ExternalSecret status
echo "5. Checking ExternalSecret status..."
echo "Current status:"
kubectl get externalsecret $EXTERNAL_SECRET_NAME -n $NAMESPACE -o jsonpath='{.status}'
echo
echo "✓ Step 5 completed"

echo
echo "All operations completed! Environment variables have been force reloaded from S3"