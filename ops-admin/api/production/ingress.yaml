apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-blacking-ops
  namespace: blacking-core
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"

spec:
  rules:
    - host: sale-api.ninjadash.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-api
                port:
                  number: 80
    - host: sale.ninjadash.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-admin
                port:
                  number: 80
    - host: api-ag.ninjadash.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-api
                port:
                  number: 80
    - host: ag.ninjadash.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-app
                port:
                  number: 80
    - host: api-game.ninjadash.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-game-api
                port:
                  number: 80
  ingressClassName: nginx
