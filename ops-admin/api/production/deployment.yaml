---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ops-api
  namespace: blacking-core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ops-api
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ops-api
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ops-api
          image: devblacking3/blacking-ops-api:v1.0.1
          imagePullPolicy: Always
          env:
            - name: ENV
              value: "production"
            - name: DB_DRIVER
              value: "postgres"
            - name: DB_HOST
              value: "db-ops-admin-do-user-6617875-0.j.db.ondigitalocean.com"
            - name: DB_PORT
              value: "25061"
            - name: DB_USER
              value: "doadmin"
            - name: DB_PASSWORD
              value : "AVNS_y9v1HfqjAw2kmKdH3UX"
            - name: DB_NAME
              value: "ops-admin-pool"
            - name: AUTO_MIGRATE
              value: "false"
            - name: PORT
              value: "8080"
            - name: JWT_SECRET
              value: "secret"
            - name: DO_TOKEN
              value : "***********************************************************************"
            - name: DO_SELF_TOKEN
              value : "***********************************************************************"
            - name: TF_BACKEND_DB
              value: "postgres://doadmin:<EMAIL>:25060/defaultdb?sslmode=require"
            - name: TF_BACKEND_SCHEMA_CLUSTER
              value: "cluster"
            - name: TF_BACKEND_SCHEMA_RESOURCE
              value: "resource"
            - name: TF_BACKEND_SCHEMA_DNS
              value: "dns"
            - name: TF_OPERATION_ENDPOINT
              value: "https://sale-api.ninjadash.app/api/v1"
            - name: CLOUDFLARE_API_TOKEN
              value: "****************************************"
            - name: CLOUDFLARE_ACCOUNT_ID
              value: "706ee7270aa740854c70a3ab5651cb2b"
            - name: CLOUDFLARE_MASTER_ZONE_ID
              value: "21ca7b229bebc1666e0fe021b84105f8"
            - name: CLOUDFLARE_MASTER_ZONE_NAME
              value: "irich.info"
            - name: DOCKERHUB_USERNAME
              value: "devblacking3"
            - name: DOCKERHUB_PASSWORD
              value: "0cs1cxmopa"
            - name: DOCKERHUB_EMAIL
              value: "<EMAIL>"
            - name: AGENT_ENDPOINT
#              value: "https://api-ag.irich.info/api/v1"
              value: "https://api-ag.ninjadash.app/api/v1"
            - name: AGENT_API_KEY
              value: "B297542697D91574|1SbzRuD3FC9y5SWmwsEFNXUA8qvqduqD"
            - name: NAMECHEAP_PROXY_ENDPOINT
              value: "https://nc-api.irich.info/api/xml.response"
            - name: NAMECHEAP_API_USER
              value: "blackingdev"
            - name: NAMECHEAP_API_KEY
              value: "c7757faf2f204b42859509ba031f0c04"
            - name: NAMECHEAP_USERNAME
              value: "blackingdev"
            - name: NAMECHEAP_CLIENT_IP
              value: "*************"
          envFrom:
            - secretRef:
                name: aws-credentials
          ports:
            - name: http
              containerPort: 8080


