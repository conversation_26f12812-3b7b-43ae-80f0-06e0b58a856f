---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ops-admin-dev
  namespace: blacking-ops-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ops-admin-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ops-admin-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ops-admin-dev
          image: devblacking3/blacking-ops-admin:dev-latest
          imagePullPolicy: Always
          env:
            - name: API_ENDPOINT
              value: "https://sale-api.irich.info/api/v1"
            - name: MASTER_DOMAIN
              value: "irich.info"
          ports:
            - name: http
              containerPort: 3000
