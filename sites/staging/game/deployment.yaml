---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-game-api-staging
  namespace: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-game-api-staging
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-game-api-staging
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-game-api-staging
          image: devblacking3/blacking-game-api:staging-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: blacking-game-api-staging-env-sync