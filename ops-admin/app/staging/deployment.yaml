---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ops-admin-staging
  namespace: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ops-admin-staging
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ops-admin-staging
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ops-admin-staging
          image: devblacking3/blacking-ops-admin:staging-latest
          imagePullPolicy: Always
          env:
            - name: API_ENDPOINT
              value: "https://sale-api-staging.irich.info/api/v1"
            - name: MASTER_DOMAIN
              value: "irich.info"
          ports:
            - name: http
              containerPort: 3000
