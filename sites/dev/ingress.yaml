apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-blacking-dev
  namespace: dev
spec:
  rules:
    - host: irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-web-dev
                port:
                  number: 3000
    - host: www.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-web-dev
                port:
                  number: 3000
    - host: admin.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-admin-dev
                port:
                  number: 3000
    - host: ag.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-app-dev
                port:
                  number: 3000
    - host: api.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-api-dev
                port:
                  number: 8080
    - host: api-game.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-game-api-dev
                port:
                  number: 8080
    - host: api-ag.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-api-dev
                port:
                  number: 8080
  ingressClassName: nginx
