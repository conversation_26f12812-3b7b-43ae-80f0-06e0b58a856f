---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ops-admin
  namespace: blacking-core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ops-admin
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ops-admin
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ops-admin
          image: devblacking3/blacking-ops-admin:v1.0.1
          imagePullPolicy: Always
          env:
            - name: API_ENDPOINT
              value: "https://sale-api.ninjadash.app/api/v1"
            - name: MASTER_DOMAIN
              value: "irich.info"
          ports:
            - name: http
              containerPort: 3000
