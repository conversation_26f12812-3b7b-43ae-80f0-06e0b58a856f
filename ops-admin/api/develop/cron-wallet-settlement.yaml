---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: wallet-settlement-cronjob
  namespace: dev
spec:
  schedule: "*/5 * * * *"  # Every 5 minutes
  timeZone: "Asia/Bangkok"
  jobTemplate:
    spec:
      ttlSecondsAfterFinished: 5  # Delete job 5 seconds after completion
      template:
        spec:
          containers:
          - name: wallet-settlement-curl
            image: curlimages/curl:latest
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - |
              echo "Starting wallet settlement API call at $(date)"
              response=$(curl -s -w "HTTPSTATUS:%{http_code}" --location 'https://api-game.irich.info/cronjob/wallet-settlement')
              http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
              body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
              
              echo "HTTP Status Code: $http_code"
              echo "Response Body: $body"
              
              if [ $http_code -eq 200 ]; then
                echo "Wallet settlement API call successful"
                exit 0
              else
                echo "Wallet settlement API call failed with status code: $http_code"
                exit 1
              fi
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
