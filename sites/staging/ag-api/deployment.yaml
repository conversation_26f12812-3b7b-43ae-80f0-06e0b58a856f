---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ag-api-staging
  namespace: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ag-api-staging
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ag-api-staging
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ag-api-staging
          image: devblacking3/blacking-ag-api:staging-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: blacking-ag-api-staging-env-sync