apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-blacking-staging
  namespace: staging
spec:
  rules:
    - host: ag-staging.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-app-staging
                port:
                  number: 3000
    - host: api-game-staging.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-game-api-staging
                port:
                  number: 8080
    - host: api-ag-staging.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ag-api-staging
                port:
                  number: 8080
  ingressClassName: nginx
