---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-admin-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-admin-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-admin-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-admin-dev
          image: devblacking3/blacking-admin:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: blacking-admin-dev-env-sync
