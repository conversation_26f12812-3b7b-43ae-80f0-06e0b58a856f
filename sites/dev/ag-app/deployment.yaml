---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ag-app-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ag-app-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ag-app-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ag-app-dev
          image: devblacking3/blacking-ag-app:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: blacking-ag-app-dev-env-sync
