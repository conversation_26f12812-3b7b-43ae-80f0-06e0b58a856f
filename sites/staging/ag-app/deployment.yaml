---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ag-app-staging
  namespace: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ag-app-staging
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ag-app-staging
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ag-app-staging
          image: devblacking3/blacking-ag-app:staging-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
          envFrom:
            - secretRef:
                name: blacking-ag-app-staging-env-sync
