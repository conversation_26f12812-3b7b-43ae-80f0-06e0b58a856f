---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-api-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-api-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-api-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-api-dev
          image: devblacking3/blacking-api:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: blacking-api-dev-env-sync