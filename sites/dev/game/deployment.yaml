---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-game-api-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-game-api-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-game-api-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-game-api-dev
          image: devblacking3/blacking-game-api:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: blacking-game-api-dev-env-sync