apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-blacking-ops-dev
  namespace: blacking-ops-dev
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"

spec:
  rules:
    - host: sale-api.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-api-dev
                port:
                  number: 80
    - host: sale.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-admin-dev
                port:
                  number: 80
  ingressClassName: nginx
