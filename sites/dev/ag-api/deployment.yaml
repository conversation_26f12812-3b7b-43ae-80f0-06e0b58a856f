---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blacking-ag-api-dev
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blacking-ag-api-dev
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: blacking-ag-api-dev
    spec:
      imagePullSecrets:
        - name: dockerhub-registrykey
      containers:
        - name: blacking-ag-api-dev
          image: devblacking3/blacking-ag-api:dev-latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          envFrom:
            - secretRef:
                name: blacking-ag-api-dev-env-sync