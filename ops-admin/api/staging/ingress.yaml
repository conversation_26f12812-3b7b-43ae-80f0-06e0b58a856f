apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-blacking-ops-staging
  namespace: staging
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"

spec:
  rules:
    - host: sale-api-staging.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-api-staging
                port:
                  number: 80
    - host: sale-staging.irich.info
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: blacking-ops-admin-staging
                port:
                  number: 80
  ingressClassName: nginx
